@echo off
chcp 65001 >nul

echo === 体彩设备管理平台 Docker 启动脚本 ===

REM 检查 Docker 是否安装
docker --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

REM 检查 Docker Compose 是否安装
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

REM 创建必要的目录
echo 创建必要的目录...
if not exist "data\attach" mkdir "data\attach"
if not exist "logs" mkdir "logs"

echo 启动服务...

REM 启动所有服务
docker-compose up -d

echo 等待服务启动...
timeout /t 30 /nobreak >nul

REM 检查服务状态
echo 检查服务状态...
docker-compose ps

echo.
echo === 服务访问地址 ===
echo 前端 Web 应用: http://localhost
echo 主后端服务: http://localhost:8080/api
echo 外部接口服务: http://localhost:8081/port-api
echo MySQL 数据库: localhost:3306
echo Redis 缓存: localhost:6379
echo.
echo === 常用命令 ===
echo 查看日志: docker-compose logs -f [服务名]
echo 停止服务: docker-compose down
echo 重启服务: docker-compose restart [服务名]
echo 查看状态: docker-compose ps
echo.
echo 启动完成！
pause
