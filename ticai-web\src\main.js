import Vue from 'vue'

// import 'default-passive-events' // 该项与地图冲突

import 'normalize.css/normalize.css' // A modern alternative to CSS resets

import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import locale from 'element-ui/lib/locale/lang/zh-CN' // lang i18n
import 'font-awesome/scss/font-awesome.scss' // 字体图标库
import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import '@/icons' // icon
import '@/permission' // permission control
import base from '@/utils/base'
import request from '@/utils/request'
import jasper from '@/utils/jasper'
import { saveAs } from 'file-saver'

import './directive/dialogDrag'
import './directive/tableHeight'
import './directive/priv'
import './directive/part'

import * as filters from './filters' // global filters
Object.keys(filters).forEach(key => { Vue.filter(key, filters[key]) })

import ECharts from 'vue-echarts'
import 'echarts'
import 'echarts-liquidfill'
Vue.component('v-chart', ECharts)
import * as echarts from 'echarts/core'
Vue.prototype.$echarts = echarts

import Tinymce from '@/components/tinymce/index.vue'
Vue.component('tinymce', Tinymce)

import axios from 'axios'
Vue.prototype.$axios = axios

Vue.config.productionTip = false
Vue.prototype.$http = request
Vue.prototype.$jasper = jasper
Vue.prototype.$saveAs = saveAs

Vue.use(base)
Vue.use(ElementUI, { locale })

new Vue({ el: '#app', router, store, render: h => h(App) })
