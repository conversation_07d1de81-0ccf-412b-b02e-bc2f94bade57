<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.4//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zy.dam.base.dao.AmLocationDAO">

    <sql id="meta">
			a.ID_
			,a.REGION_
			,a.CODE_
			,a.NAME_
			,a.CONTACT_
			,a.PHONE_
			,a.ADDRESS_
			,a.LNG_
			,a.LAT_
			,a.MEMO_
			,a.FLAG_
	</sql>
    <!-- 插入数据 -->
    <insert id="insert" parameterType="com.zy.dam.base.orm.AmLocation">
        <selectKey resultType="java.lang.String" keyProperty="id" order="BEFORE">select uuid()</selectKey>
        insert into AM_LOCATION(ID_,REGION_,CODE_,NAME_,CONTACT_,PHONE_,ADDRESS_,LNG_,LAT_,MEMO_,FLAG_)
        values(#{id},#{region},#{code},#{name},#{contact},#{phone},#{address},#{lng},#{lat},#{memo},'1')
    </insert>

    <!-- 更新数据 -->
    <update id="update" parameterType="com.zy.dam.base.orm.AmLocation">
		update AM_LOCATION
		set CODE_=#{code},NAME_=#{name},CONTACT_=#{contact},PHONE_=#{phone},ADDRESS_=#{address},LNG_=#{lng},LAT_=#{lat},MEMO_=#{memo}
		where ID_=#{id}
	</update>

    <select id="findIdByCode" resultType="String">
		select ID_ from AM_LOCATION where FLAG_='1' and CODE_=#{0}
	</select>

    <!-- 分页 -->
    <select id="page" resultType="com.zy.dam.base.vo.LocationVo">
        select
        <include refid="meta"/>,b.SN_, b.TIME_
        ,(select m.NAME_ from AM_REGION m where m.ID_=a.REGION_) region_name
        from AM_LOCATION a LEFT JOIN AM_LOCATION_ASSET b on a.ID_ = b.LOCATION_
        where a.FLAG_='1' and b.FLAG_='1'
        <if test="region != null">and a.REGION_=#{region}</if>
        <if test="keyword != null">and (a.CODE_ like concat('%',#{keyword},'%') or a.NAME_ like
            concat('%',#{keyword},'%')
            or a.PHONE_ like concat('%',#{keyword},'%') or a.ADDRESS_ like concat('%',#{keyword},'%')
            or b.SN_ like concat('%',#{keyword},'%')
            )
        </if>
        <if test="timeBegin != null">and DATE(b.TIME_) &gt;= #{timeBegin}</if>
        <if test="timeEnd != null">and DATE(b.TIME_) &lt;= #{timeEnd}</if>
        order by a.CODE_
    </select>

    <!-- 删除 -->
    <update id="delete">
		update AM_LOCATION set FLAG_='9' where ID_=#{0}
	</update>

    <select id="listOption" resultType="com.zy.model.ValueLabel">
        select ID_ value_,NAME_ label  from AM_LOCATION a where a.FLAG_='1' and a.REGION_=#{0} order by a.CODE_
    </select>

    <select id="findAll" resultType="com.zy.dam.base.orm.AmLocation">
        select ID_,REGION_,NAME_ from AM_LOCATION a where a.FLAG_='1' order by a.CODE_
    </select>

    <select id="findByRegion" resultType="com.zy.dam.base.orm.AmLocation">
        select
        <include refid="meta"/>
        from AM_LOCATION a
        where a.FLAG_='1' and REGION_=#{0} order by a.CODE_
    </select>

    <select id="listRegion" resultType="com.zy.model.VueTreeNode">
        select a.ID_, '0' REGION_,a.NAME_ label_
		from AM_REGION a
        where a.FLAG_='1'and a.REGION_ != '0'
        order by a.ORD_,a.CODE_
    </select>

    <select id="listLocation" resultType="com.zy.model.VueTreeNode">
		select a.ID_,a.REGION_ pid,a.NAME_ label_, 1 leaf_
        from AM_LOCATION a
        where a.FLAG_='1'
    </select>

    <select id="findLocationByCode" resultType="String">
        select ID_ from AM_LOCATION where FLAG_='1' and CODE_=#{0} limit 0,1
    </select>

    <select id="findIdBySn" resultType="String">
		select ID_ from AM_LOCATION_ASSET where FLAG_='1' and SN_=#{0}
	</select>

    <update id="deleteItem">
		update AM_LOCATION_ASSET set FLAG_='9' where FLAG_='1' and LOCATION_=#{0}
	</update>

    <insert id="insertItem">
		insert into AM_LOCATION_ASSET(ID_,LOCATION_,SN_,ASSET_,TIME_,USER_,FLAG_) values(uuid(),#{location},#{sn},#{asset},#{time},#{user},'1')
    </insert>

    <select id="findVo" resultType="com.zy.dam.base.vo.AmLocationVo">
        select
        <include refid="meta"/>
        from AM_LOCATION a where a.ID_=#{0}
    </select>

    <select id="findLocationAsset" resultType="com.zy.dam.base.orm.AmLocationAsset">
        select f.ID_,f.LOCATION_,f.SN_,f.ASSET_,f.TIME_,f.USER_,f.FLAG_
        from AM_LOCATION_ASSET f LEFT JOIN AM_LOCATION u ON f.LOCATION_=u.ID_
        WHERE f.FLAG_=1 and u.ID_=#{0};
    </select>

</mapper>
