<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1917ff97-bac5-494c-931c-9b0058e5e94d" name="Changes" comment="fix：合并" />
    <list id="e21cad75-b6af-412c-b3ce-2512baa88706" name="no push" comment="no push">
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/application-staging.yml" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/application-staging.yml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="fix/header" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Ling90F&quot;
  }
}</component>
  <component name="GitToolBoxStore">
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="develop" />
                    <option name="lastUsedInstant" value="1750822615" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="fix/header" />
                    <option name="lastUsedInstant" value="1750734592" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="fix/cookie" />
                    <option name="lastUsedInstant" value="1750734588" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature_bug" />
                    <option name="lastUsedInstant" value="1749611820" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="staging" />
                    <option name="lastUsedInstant" value="**********" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/hntsz/ticai-dam-port.git&quot;,
    &quot;accountId&quot;: &quot;88890fd6-033d-4b89-a15d-ee14e476514f&quot;
  }
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\maven\apache-maven-3.8.1" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="E:\maven\apache-maven-3.8.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2xcnRmXLIqUDLz7ppd486ad5eqU" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.dam-port [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.dam-port [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.dam-port [package].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.DamPortApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;develop&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;F:/work/ticai/ticai-dam-port&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="F:\work\ticai\ticai-dam-port\src\main\resources" />
    </key>
  </component>
  <component name="RunManager">
    <configuration name="DamPortApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="dam-port" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zy.dam.DamPortApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.23774.435" />
        <option value="bundled-js-predefined-d6986cc7102b-f27c65a3e318-JavaScript-IU-251.23774.435" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="1917ff97-bac5-494c-931c-9b0058e5e94d" name="Changes" comment="" />
      <changelist id="e21cad75-b6af-412c-b3ce-2512baa88706" name="no push" comment="" />
      <created>1748246224735</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1748246224735</updated>
      <workItem from="1748246227138" duration="8109000" />
      <workItem from="1748306954092" duration="1750000" />
      <workItem from="1748308785195" duration="8839000" />
      <workItem from="1748328456446" duration="12110000" />
      <workItem from="1748393038884" duration="11392000" />
      <workItem from="1748414234467" duration="3345000" />
      <workItem from="1748417779766" duration="4752000" />
      <workItem from="1748504115132" duration="7915000" />
      <workItem from="1748567751114" duration="6232000" />
      <workItem from="1748912926466" duration="6840000" />
      <workItem from="1748940357970" duration="1233000" />
      <workItem from="1749004573338" duration="9012000" />
      <workItem from="1749017896068" duration="12061000" />
      <workItem from="1749085685584" duration="11835000" />
      <workItem from="1749172586591" duration="12754000" />
      <workItem from="1749203137772" duration="607000" />
      <workItem from="1749431269653" duration="6693000" />
      <workItem from="1749521429869" duration="5620000" />
      <workItem from="1749537223902" duration="7091000" />
      <workItem from="1749607968229" duration="8767000" />
      <workItem from="1749691036259" duration="17925000" />
      <workItem from="1749797695638" duration="122000" />
      <workItem from="1750153346327" duration="1401000" />
      <workItem from="1750209135628" duration="15911000" />
      <workItem from="1750315467890" duration="70000" />
      <workItem from="1750321808539" duration="585000" />
      <workItem from="1750649936938" duration="17573000" />
      <workItem from="1750727062047" duration="11066000" />
      <workItem from="1750758373385" duration="19000" />
      <workItem from="1750822466318" duration="6733000" />
      <workItem from="1750836599546" duration="6195000" />
      <workItem from="1750900768791" duration="3597000" />
      <workItem from="1750920008091" duration="401000" />
      <workItem from="1750922529203" duration="3886000" />
      <workItem from="1750928254935" duration="3043000" />
    </task>
    <task id="LOCAL-00001" summary="feat：录入工单功能优化&#10;- 取消【终端号】、【网点用户】和【网点地址】的必填限制&#10;- 增加【资产编码】字段">
      <option name="closed" value="true" />
      <created>1748316046262</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1748316046262</updated>
    </task>
    <task id="LOCAL-00002" summary="perf：修改资产查询数据">
      <option name="closed" value="true" />
      <created>1748334382497</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1748334382497</updated>
    </task>
    <task id="LOCAL-00003" summary="fix: 修改资产分页查询">
      <option name="closed" value="true" />
      <created>1748340026344</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1748340026344</updated>
    </task>
    <task id="LOCAL-00004" summary="feat：筛选条件">
      <option name="closed" value="true" />
      <created>1748402523407</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1748402523407</updated>
    </task>
    <task id="LOCAL-00005" summary="feat：筛选条件">
      <option name="closed" value="true" />
      <created>1748421656066</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1748421656066</updated>
    </task>
    <task id="LOCAL-00006" summary="fix：筛选条件">
      <option name="closed" value="true" />
      <created>1748422342129</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1748422342129</updated>
    </task>
    <task id="LOCAL-00007" summary="perf：优化绑定资产分页查询">
      <option name="closed" value="true" />
      <created>1748508446232</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1748508446232</updated>
    </task>
    <task id="LOCAL-00008" summary="fix：统一AM_ASSET表查询">
      <option name="closed" value="true" />
      <created>1748511022193</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1748511022193</updated>
    </task>
    <task id="LOCAL-00009" summary="fix：资产终端机绑定统计&#10;- 修改资产终端机绑定统计查询数据条件">
      <option name="closed" value="true" />
      <created>1748925359433</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1748925359433</updated>
    </task>
    <task id="LOCAL-00010" summary="fix：资产终端机绑定统计&#10;- 修改资产终端机绑定统计查询数据条件">
      <option name="closed" value="true" />
      <created>1748926134437</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1748926134437</updated>
    </task>
    <task id="LOCAL-00011" summary="fix：设备管理查询&#10;- 修改领用和维修操作时记录">
      <option name="closed" value="true" />
      <created>1749004658489</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1749004658489</updated>
    </task>
    <task id="LOCAL-00012" summary="fix：修改配置">
      <option name="closed" value="true" />
      <created>1749008338189</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1749008338189</updated>
    </task>
    <task id="LOCAL-00013" summary="fix：修改配置">
      <option name="closed" value="true" />
      <created>1749008588389</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1749008588389</updated>
    </task>
    <task id="LOCAL-00014" summary="fix：修改redis端口配置">
      <option name="closed" value="true" />
      <created>1749008924084</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1749008924084</updated>
    </task>
    <task id="LOCAL-00015" summary="fix：修改redis端口配置">
      <option name="closed" value="true" />
      <created>1749009342722</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1749009342722</updated>
    </task>
    <task id="LOCAL-00016" summary="fix：增加openid测试">
      <option name="closed" value="true" />
      <created>1749026128326</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1749026128326</updated>
    </task>
    <task id="LOCAL-00017" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749031247997</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1749031247997</updated>
    </task>
    <task id="LOCAL-00018" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749031830961</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1749031830961</updated>
    </task>
    <task id="LOCAL-00019" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749089363239</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1749089363239</updated>
    </task>
    <task id="LOCAL-00020" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749090220823</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1749090220823</updated>
    </task>
    <task id="LOCAL-00021" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749091208173</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1749091208173</updated>
    </task>
    <task id="LOCAL-00022" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749092099371</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1749092099371</updated>
    </task>
    <task id="LOCAL-00023" summary="fix：添加调试信息">
      <option name="closed" value="true" />
      <created>1749093056175</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1749093056175</updated>
    </task>
    <task id="LOCAL-00024" summary="fix：日期筛选排除网点数据">
      <option name="closed" value="true" />
      <created>1749189172329</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1749189172329</updated>
    </task>
    <task id="LOCAL-00025" summary="fix：时区问题">
      <option name="closed" value="true" />
      <created>1749189184670</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1749189184670</updated>
    </task>
    <task id="LOCAL-00026" summary="fix：扫码定位&#10;- 时区问题">
      <option name="closed" value="true" />
      <created>1749195680794</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1749195680794</updated>
    </task>
    <task id="LOCAL-00027" summary="fix：扫码定位&#10;- 时区问题">
      <option name="closed" value="true" />
      <created>1749198481942</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1749198481942</updated>
    </task>
    <task id="LOCAL-00028" summary="fix：修复手机验证码验证规则">
      <option name="closed" value="true" />
      <created>1749439933423</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1749439933423</updated>
    </task>
    <task id="LOCAL-00029" summary="fix：资产查询&#10;- 修改资产查询逻辑">
      <option name="closed" value="true" />
      <created>1749522103932</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1749522103932</updated>
    </task>
    <task id="LOCAL-00030" summary="fix：资产终端机绑定统计&#10;- 增加判断条件">
      <option name="closed" value="true" />
      <created>1749608781065</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1749608781065</updated>
    </task>
    <task id="LOCAL-00031" summary="fix：资产终端机绑定统计&#10;- 修改判断是否绑定逻辑">
      <option name="closed" value="true" />
      <created>1749609079570</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1749609079570</updated>
    </task>
    <task id="LOCAL-00032" summary="fix：资产终端机绑定统计&#10;- 修改用户信息">
      <option name="closed" value="true" />
      <created>1749609497844</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1749609497844</updated>
    </task>
    <task id="LOCAL-00033" summary="fix：资产查询&#10;- 优化查询逻辑">
      <option name="closed" value="true" />
      <created>1749696445522</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1749696445522</updated>
    </task>
    <task id="LOCAL-00034" summary="fix：资产查询&#10;- 新增业主名称查询逻辑">
      <option name="closed" value="true" />
      <created>1749697184125</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1749697184125</updated>
    </task>
    <task id="LOCAL-00035" summary="fix：资产查询&#10;- 新增业主名称查询逻辑">
      <option name="closed" value="true" />
      <created>1749698019894</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1749698019894</updated>
    </task>
    <task id="LOCAL-00036" summary="fix">
      <option name="closed" value="true" />
      <created>1750650042435</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1750650042435</updated>
    </task>
    <task id="LOCAL-00037" summary="fix：添加secure和httpOnly属性">
      <option name="closed" value="true" />
      <created>1750731126928</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1750731126928</updated>
    </task>
    <task id="LOCAL-00038" summary="fix：Cookie安全拦截器">
      <option name="closed" value="true" />
      <created>1750733248710</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1750733248710</updated>
    </task>
    <task id="LOCAL-00039" summary="fix：设置响应头&#10;- 修复web应用服务器版本信息泄露问题&#10;- X-Content-Type-Options响应头&#10;- 配置X-Frame-Options&#10;- X-XSS-Protection响应头&#10;- Referrer-Policy响应头">
      <option name="closed" value="true" />
      <created>1750734685407</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1750734685407</updated>
    </task>
    <task id="LOCAL-00040" summary="fix：设置响应头&#10;- Content-Security-Policy响应头&#10;- X-Permitted-Cross-Domain-Policies响应头&#10;- X-Download-Options响应头&#10;- Strict-Transport-Security">
      <option name="closed" value="true" />
      <created>1750822584573</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1750822584573</updated>
    </task>
    <task id="LOCAL-00041" summary="fix：创建dockerfile文件">
      <option name="closed" value="true" />
      <created>1750927328601</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1750927328601</updated>
    </task>
    <task id="LOCAL-00042" summary="fix：合并">
      <option name="closed" value="true" />
      <created>1750927652277</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1750927652277</updated>
    </task>
    <option name="localTasksCounter" value="43" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="develop" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="perf：优化绑定资产分页查询" />
    <MESSAGE value="fix：统一AM_ASSET表查询" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改资产终端机绑定统计查询数据条件" />
    <MESSAGE value="fix：设备管理查询&#10;- 修改领用和维修操作时记录" />
    <MESSAGE value="fix：修改配置" />
    <MESSAGE value="fix：修改redis端口配置" />
    <MESSAGE value="fix：增加openid测试" />
    <MESSAGE value="fix：添加调试信息" />
    <MESSAGE value="fix：日期筛选排除网点数据" />
    <MESSAGE value="fix：时区问题" />
    <MESSAGE value="fix：扫码定位&#10;- 时区问题" />
    <MESSAGE value="fix：修复手机验证码验证规则" />
    <MESSAGE value="fix：资产查询&#10;- 修改资产查询逻辑" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 增加判断条件" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改判断是否绑定逻辑" />
    <MESSAGE value="fix：资产终端机绑定统计&#10;- 修改用户信息" />
    <MESSAGE value="fix：资产查询&#10;- 优化查询逻辑" />
    <MESSAGE value="fix：资产查询&#10;- 新增业主名称查询逻辑" />
    <MESSAGE value="fix" />
    <MESSAGE value="fix：添加secure和httpOnly属性" />
    <MESSAGE value="fix：Cookie安全拦截器" />
    <MESSAGE value="fix：设置响应头&#10;- 修复web应用服务器版本信息泄露问题&#10;- X-Content-Type-Options响应头&#10;- 配置X-Frame-Options&#10;- X-XSS-Protection响应头&#10;- Referrer-Policy响应头" />
    <MESSAGE value="fix：设置响应头&#10;- Content-Security-Policy响应头&#10;- X-Permitted-Cross-Domain-Policies响应头&#10;- X-Download-Options响应头&#10;- Strict-Transport-Security" />
    <MESSAGE value="fix：创建dockerfile文件" />
    <MESSAGE value="fix：合并" />
    <option name="LAST_COMMIT_MESSAGE" value="fix：合并" />
  </component>
</project>