#!/bin/bash

# 安全响应头验证脚本
# 用于验证修复后的安全响应头是否正确设置

echo "=== 安全响应头验证脚本 ==="
echo "开始验证安全响应头修复效果..."
echo ""

# 定义颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 验证函数
check_header() {
    local url=$1
    local header_name=$2
    local expected_value=$3
    local service_name=$4
    
    echo -n "检查 $service_name - $header_name: "
    
    # 获取响应头
    response=$(curl -s -I "$url" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 无法连接到服务${NC}"
        return 1
    fi
    
    # 检查是否包含指定的响应头
    if echo "$response" | grep -i "$header_name" | grep -q "$expected_value"; then
        echo -e "${GREEN}✅ 已正确设置${NC}"
        return 0
    else
        echo -e "${RED}❌ 未找到或值不正确${NC}"
        echo "   期望: $header_name: $expected_value"
        echo "   实际响应头:"
        echo "$response" | grep -i "$header_name" | sed 's/^/   /'
        return 1
    fi
}

# 验证服务器信息隐藏
check_server_hidden() {
    local url=$1
    local service_name=$2
    
    echo -n "检查 $service_name - 服务器信息隐藏: "
    
    response=$(curl -s -I "$url" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 无法连接到服务${NC}"
        return 1
    fi
    
    # 检查是否隐藏了Server头
    if echo "$response" | grep -i "server:" | grep -v "^\s*server:\s*$"; then
        echo -e "${RED}❌ 仍然暴露服务器信息${NC}"
        echo "$response" | grep -i "server:" | sed 's/^/   /'
        return 1
    else
        echo -e "${GREEN}✅ 服务器信息已隐藏${NC}"
        return 0
    fi
}

# 测试URL配置
BACKEND_URL="http://localhost:8080/api/"
PORT_URL="http://localhost:8080/port-api/"
FRONTEND_URL="http://localhost:80/"

echo "=== 测试后端主服务 (ticai-dam) ==="
check_header "$BACKEND_URL" "X-Content-Type-Options" "nosniff" "后端主服务"
check_header "$BACKEND_URL" "X-Frame-Options" "DENY" "后端主服务"
check_header "$BACKEND_URL" "X-XSS-Protection" "1; mode=block" "后端主服务"
check_header "$BACKEND_URL" "Referrer-Policy" "strict-origin-when-cross-origin" "后端主服务"
check_header "$BACKEND_URL" "Content-Security-Policy" "default-src 'self'" "后端主服务"
check_header "$BACKEND_URL" "X-Permitted-Cross-Domain-Policies" "none" "后端主服务"
check_header "$BACKEND_URL" "X-Download-Options" "noopen" "后端主服务"
check_header "$BACKEND_URL" "Strict-Transport-Security" "max-age=31536000" "后端主服务"
check_server_hidden "$BACKEND_URL" "后端主服务"

echo ""
echo "=== 测试端口服务 (ticai-dam-port) ==="
check_header "$PORT_URL" "X-Content-Type-Options" "nosniff" "端口服务"
check_header "$PORT_URL" "X-Frame-Options" "DENY" "端口服务"
check_header "$PORT_URL" "X-XSS-Protection" "1; mode=block" "端口服务"
check_header "$PORT_URL" "Referrer-Policy" "strict-origin-when-cross-origin" "端口服务"
check_header "$PORT_URL" "Content-Security-Policy" "default-src 'self'" "端口服务"
check_header "$PORT_URL" "X-Permitted-Cross-Domain-Policies" "none" "端口服务"
check_header "$PORT_URL" "X-Download-Options" "noopen" "端口服务"
check_header "$PORT_URL" "Strict-Transport-Security" "max-age=31536000" "端口服务"
check_server_hidden "$PORT_URL" "端口服务"

echo ""
echo "=== 测试前端服务 (nginx) ==="
check_header "$FRONTEND_URL" "X-Content-Type-Options" "nosniff" "前端服务"
check_header "$FRONTEND_URL" "X-Frame-Options" "DENY" "前端服务"
check_header "$FRONTEND_URL" "X-XSS-Protection" "1; mode=block" "前端服务"
check_header "$FRONTEND_URL" "Referrer-Policy" "strict-origin-when-cross-origin" "前端服务"
check_header "$FRONTEND_URL" "Content-Security-Policy" "default-src 'self'" "前端服务"
check_header "$FRONTEND_URL" "X-Permitted-Cross-Domain-Policies" "none" "前端服务"
check_header "$FRONTEND_URL" "X-Download-Options" "noopen" "前端服务"
check_header "$FRONTEND_URL" "Strict-Transport-Security" "max-age=31536000" "前端服务"
check_server_hidden "$FRONTEND_URL" "前端服务"

echo ""
echo "=== 验证完成 ==="
echo -e "${YELLOW}注意事项:${NC}"
echo "1. 如果某些服务无法连接，请确保服务已启动"
echo "2. Strict-Transport-Security 头只在HTTPS环境下有效"
echo "3. 如果测试失败，请检查服务是否正确重启"
echo ""
echo "修复的安全响应头说明:"
echo "• Content-Security-Policy: 防止XSS攻击"
echo "• X-Permitted-Cross-Domain-Policies: 防止跨域策略文件攻击"
echo "• X-Download-Options: 防止IE自动打开下载文件"
echo "• Strict-Transport-Security: 强制HTTPS传输"
