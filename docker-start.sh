#!/bin/bash

# 体彩设备管理平台 Docker 启动脚本

echo "=== 体彩设备管理平台 Docker 启动脚本 ==="

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "错误: Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p data/attach
mkdir -p logs

# 设置目录权限
chmod 755 data/attach
chmod 755 logs

echo "启动服务..."

# 启动所有服务
docker-compose up -d

echo "等待服务启动..."
sleep 30

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

echo ""
echo "=== 服务访问地址 ==="
echo "前端 Web 应用: http://localhost"
echo "主后端服务: http://localhost:8080/api"
echo "外部接口服务: http://localhost:8081/port-api"
echo "MySQL 数据库: localhost:3306"
echo "Redis 缓存: localhost:6379"
echo ""
echo "=== 常用命令 ==="
echo "查看日志: docker-compose logs -f [服务名]"
echo "停止服务: docker-compose down"
echo "重启服务: docker-compose restart [服务名]"
echo "查看状态: docker-compose ps"
echo ""
echo "启动完成！"
