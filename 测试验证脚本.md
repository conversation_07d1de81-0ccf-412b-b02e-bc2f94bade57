# 服务器版本信息泄露修复验证脚本

## 验证步骤

### 1. 编译和启动应用

```bash
# 编译ticai-dam项目
cd ticai-dam
mvn clean package -DskipTests

# 编译ticai-dam-port项目
cd ../ticai-dam-port
mvn clean package -DskipTests

# 启动应用
java -jar target/dam-1.0.0.jar
java -jar target/dam-port-1.0.0.jar
```

### 2. 测试 HTTP 响应头

```bash
# 测试主应用API响应头
curl -I http://localhost:8080/api/test

# 测试端口应用API响应头
curl -I http://localhost:8080/port-api/test

# 检查响应头中是否包含：
# - Server: 应该为空或不存在
# - X-Powered-By: 应该不存在
# - X-Content-Type-Options: nosniff
# - X-Frame-Options: DENY
# - X-XSS-Protection: 1; mode=block
# - Referrer-Policy: strict-origin-when-cross-origin
# - Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; ...
# - X-Permitted-Cross-Domain-Policies: none
# - X-Download-Options: noopen
# - Strict-Transport-Security: max-age=31536000; includeSubDomains
```

### 3. 测试错误页面

```bash
# 测试404错误
curl http://localhost:8080/api/nonexistent

# 测试500错误（如果有触发方式）
curl http://localhost:8080/api/error-trigger

# 检查响应是否：
# - 不包含服务器版本信息
# - 不包含技术栈信息
# - 返回用户友好的错误信息
```

### 4. 测试前端错误页面

```bash
# 启动nginx（如果使用）
nginx -t
nginx -s reload

# 访问不存在的页面
curl http://localhost/nonexistent

# 检查是否返回自定义404页面
```

### 5. 测试管理端点

```bash
# 测试健康检查端点
curl http://localhost:8080/api/actuator/health

# 测试其他端点是否被禁用
curl http://localhost:8080/api/actuator/info
curl http://localhost:8080/api/actuator/env

# 健康检查应该可访问但不显示详细信息
# 其他端点应该返回404或被禁用
```

## 预期结果

### ✅ 成功标准：

1. **HTTP 响应头安全**：

   - 不包含 `Server` 头或为空
   - 不包含 `X-Powered-By` 头
   - 包含安全响应头（X-Content-Type-Options, X-Frame-Options 等）

2. **错误处理安全**：

   - 404 错误返回用户友好信息，不暴露技术细节
   - 500 错误返回通用错误信息
   - 异常信息不暴露给用户

3. **管理端点安全**：

   - 只有 health 端点可访问
   - health 端点不显示详细信息
   - 其他管理端点被禁用

4. **前端错误页面**：
   - 自定义 404 页面正常显示
   - 不包含服务器技术信息

### ❌ 失败标准：

1. 响应头中仍包含服务器版本信息
2. 错误页面暴露技术栈信息
3. 异常信息直接返回给用户
4. 管理端点暴露敏感信息

## 问题排查

### 如果仍有版本信息泄露：

1. **检查 Nginx 配置**：

   ```bash
   nginx -T | grep server_tokens
   ```

2. **检查 Spring Boot 配置**：

   ```bash
   grep -r "server-header" src/main/resources/
   ```

3. **检查过滤器是否生效**：

   - 查看应用启动日志
   - 确认 SecurityHeadersFilter 被注册

4. **检查错误处理器**：
   - 确认 CustomErrorController 被加载
   - 查看 ErrorHandler 是否正确处理异常

### 常见问题解决：

1. **过滤器未生效**：

   - 检查@Component 注解
   - 确认 SecurityConfig 配置正确

2. **错误页面未生效**：

   - 检查 application.yml 中的 error 配置
   - 确认 CustomErrorController 路径正确

3. **Nginx 配置未生效**：
   - 重新加载 Nginx 配置
   - 检查配置文件语法

## 安全验证工具

### 使用安全扫描工具验证：

```bash
# 使用nmap检查服务
nmap -sV -p 8080 localhost

# 使用curl详细检查响应头
curl -v http://localhost:8080/api/

# 专门测试新增的安全响应头
curl -s -D- http://localhost:8080/api/ | grep -E "(Content-Security-Policy|X-Permitted-Cross-Domain-Policies|X-Download-Options|Strict-Transport-Security)"

# 测试前端nginx安全头
curl -s -D- http://localhost:80/ | grep -E "(Content-Security-Policy|X-Permitted-Cross-Domain-Policies|X-Download-Options|Strict-Transport-Security)"

# 使用在线工具检查安全头
# https://securityheaders.com/
```

### 日志监控：

```bash
# 监控应用日志
tail -f logs/dam.log

# 检查是否有异常信息泄露
grep -i "exception\|error\|stack" logs/dam.log
```

## 修复验证清单

### 基础安全检查：

- [ ] HTTP 响应头不包含服务器版本信息
- [ ] 错误页面不暴露技术栈信息
- [ ] 异常处理安全，不泄露敏感信息
- [ ] 管理端点访问受限
- [ ] 自定义错误页面正常工作

### 新增安全响应头检查：

- [ ] Content-Security-Policy 响应头已正确设置
- [ ] X-Permitted-Cross-Domain-Policies: none 已设置
- [ ] X-Download-Options: noopen 已设置
- [ ] Strict-Transport-Security 已设置（HTTPS 环境）

### 功能验证：

- [ ] 应用功能正常，无回归问题
- [ ] 前端页面正常加载和显示
- [ ] API 接口正常响应

完成所有验证后，确认服务器版本信息泄露问题已完全修复。
