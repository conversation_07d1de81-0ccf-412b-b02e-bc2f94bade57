06-24 10:09:37.333 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:09:37.335 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 14984 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:09:37.338 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:09:38.107 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:09:38.108 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:09:38.131 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
06-24 10:09:38.303 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=d6520942-83a0-3c6b-8ff0-ecde9fb7ca04
06-24 10:09:38.588 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:09:38.594 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:09:38.595 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:09:38.595 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:09:38.761 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:09:38.761 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1389 ms
06-24 10:09:38.792 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:09:38.856 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:09:39.589 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:09:40.629 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:09:40.629 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:09:40.635 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:09:40.635 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:09:40.635 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:09:40.636 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:09:40.635781200+08:00[Asia/Shanghai]
06-24 10:09:40.723 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:09:40.765 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:09:40.790 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:09:40.825 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:09:41
06-24 10:09:40.826 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:09:41
06-24 10:09:40.880 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750730980826
06-24 10:09:40.881 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750730981000
06-24 10:09:40.881 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 174 毫秒
06-24 10:09:40.881 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:09:40.881 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:09:41.836 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:09:41.845 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:09:41.846 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:09:41.846 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:09:41.846 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:09:41.846 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:09:41.846 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:09:41.846 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@2d2fe68a
06-24 10:09:43.051 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:09:43.055 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:09:43.136 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:09:43.195 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:09:43.220 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:09:44.390 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:09:44.390 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:09:44.390 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:09:44.399 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 8.57 seconds (JVM running for 9.58)
06-24 10:10:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:10:00.186 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:10:00.215 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:10:00.245 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:11:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:11:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:11:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:11:00.230 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:12:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:12:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:12:00.201 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:12:00.226 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:13:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:13:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:13:00.155 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:13:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:14:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:14:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:14:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:14:00.248 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:14:32.589 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:14:32.590 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:14:32.591 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
06-24 10:14:37.199 [http-nio-20000-exec-2] WARN  org.apache.catalina.util.SessionIdGeneratorBase - Creation of SecureRandom instance for session ID generation using [SHA1PRNG] took [126] milliseconds.
06-24 10:15:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:15:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:15:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:15:00.245 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:15:31.521 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:15:31.525 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:15:31.557 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:16:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:16:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:16:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:16:00.205 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:17:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:17:00.170 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:17:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:17:00.243 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:18:00.091 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:18:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:18:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:18:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:19:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:19:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:19:00.213 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:19:00.261 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:19:51.762 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:19:51.916 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 10:19:51.916 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 10:19:51.916 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:19:51.917 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 10:19:52.043 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 10:19:52.064 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 10:19:58.104 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:19:58.107 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 10216 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:19:58.109 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:19:58.759 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:19:58.761 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:19:58.782 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
06-24 10:19:58.947 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=4d80e388-6561-3c99-bb6b-e9ea4f3d9c37
06-24 10:19:59.214 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:19:59.221 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:19:59.221 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:19:59.221 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:19:59.325 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:19:59.325 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1191 ms
06-24 10:19:59.351 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:19:59.397 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:19:59.984 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:20:00.807 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:20:00.807 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:20:00.813 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:20:00.813 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:20:00.813 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:20:00.813 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:20:00.813215400+08:00[Asia/Shanghai]
06-24 10:20:00.906 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:20:00.964 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:20:00.992 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:20:01.018 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:20:01
06-24 10:20:01.018 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:20:01
06-24 10:20:01.065 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750731601018
06-24 10:20:01.065 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750731601000
06-24 10:20:01.065 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 18 毫秒
06-24 10:20:01.065 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:20:01.065 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:20:01.702 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:20:01.706 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:20:01.706 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:20:01.707 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:20:01.707 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:20:01.707 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:20:01.707 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:20:01.707 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@70b0dc92
06-24 10:20:02.898 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:20:02.903 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:20:02.940 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:20:02.982 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:20:02.996 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:20:04.169 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:20:04.169 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:20:04.169 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:20:04.178 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.689 seconds (JVM running for 8.579)
06-24 10:20:15.859 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:20:15.860 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:20:15.862 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
06-24 10:20:16.872 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:20:16.878 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:20:16.944 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:20:29.534 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:20:29.692 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 10:20:29.692 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 10:20:29.692 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:20:29.692 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 10:20:29.815 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 10:20:29.838 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 10:20:33.193 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:20:33.194 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 46352 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:20:33.196 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:20:33.763 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:20:33.766 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:20:33.784 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
06-24 10:20:33.952 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=4d80e388-6561-3c99-bb6b-e9ea4f3d9c37
06-24 10:20:34.198 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:20:34.203 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:20:34.204 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:20:34.204 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:20:34.298 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:20:34.298 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1078 ms
06-24 10:20:34.324 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:20:34.375 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:20:34.897 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:20:35.708 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:20:35.708 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:20:35.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:20:35.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:20:35.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:20:35.715 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:20:35.715335200+08:00[Asia/Shanghai]
06-24 10:20:35.791 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:20:35.816 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:20:35.839 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:20:35.887 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:20:36
06-24 10:20:35.887 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:20:36
06-24 10:20:35.914 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750731635887
06-24 10:20:35.914 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750731636000
06-24 10:20:35.914 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 113 毫秒
06-24 10:20:35.914 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:20:35.914 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:20:36.581 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:20:36.587 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:20:36.587 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:20:36.587 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:20:36.588 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:20:36.588 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:20:36.588 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:20:36.588 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@df04d12
06-24 10:20:37.783 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:20:37.787 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:20:37.822 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:20:37.861 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:20:37.876 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:20:39.052 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:20:39.052 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:20:39.052 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:20:39.061 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.372 seconds (JVM running for 8.034)
06-24 10:20:56.008 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:20:56.008 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:20:56.010 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
06-24 10:20:56.971 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:20:56.974 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:20:57.058 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:21:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:21:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:21:00.213 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:21:00.258 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:22:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:22:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:22:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:22:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:23:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:23:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:23:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:23:00.215 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:24:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:24:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:24:00.155 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:24:00.210 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:24:08.928 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:24:09.119 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 10:24:09.120 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 10:24:09.120 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:24:09.120 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 10:24:09.256 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 10:24:09.277 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 10:24:14.308 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:24:14.310 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 20108 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:24:14.311 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:24:14.968 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:24:14.969 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:24:14.988 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
06-24 10:24:15.153 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=4d80e388-6561-3c99-bb6b-e9ea4f3d9c37
06-24 10:24:15.392 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:24:15.399 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:24:15.400 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:24:15.400 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:24:15.498 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:24:15.499 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1163 ms
06-24 10:24:15.527 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:24:15.574 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:24:16.106 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:24:17.004 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:24:17.004 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:24:17.010 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:24:17.011 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:24:17.011 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:24:17.011 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:24:17.011010200+08:00[Asia/Shanghai]
06-24 10:24:17.116 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:24:17.163 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:24:17.189 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:24:17.215 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:24:17
06-24 10:24:17.216 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:24:17
06-24 10:24:17.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750731857216
06-24 10:24:17.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750731857000
06-24 10:24:17.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 216 毫秒
06-24 10:24:17.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:24:17.266 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:24:17.930 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:24:17.935 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:24:17.935 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:24:17.935 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:24:17.936 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:24:17.936 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:24:17.936 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:24:17.936 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@51d34f02
06-24 10:24:19.123 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:24:19.130 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:24:19.172 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:24:19.218 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:24:19.234 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:24:20.410 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:24:20.410 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:24:20.410 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:24:20.418 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.688 seconds (JVM running for 8.449)
06-24 10:24:27.432 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:24:27.433 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:24:27.434 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
06-24 10:24:28.525 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:24:28.530 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:24:28.609 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:24:37.295 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:24:37.511 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 10:24:37.512 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 10:24:37.512 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:24:37.512 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 10:24:37.634 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 10:24:37.660 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 10:24:42.566 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:24:42.568 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 14032 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:24:42.569 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:24:43.217 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:24:43.218 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:24:43.237 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
06-24 10:24:43.404 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=ba6bd1ae-5a34-35d5-ad44-95dd18e02c33
06-24 10:24:43.668 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:24:43.676 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:24:43.676 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:24:43.676 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:24:43.785 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:24:43.785 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1190 ms
06-24 10:24:43.812 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:24:43.862 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:24:44.487 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:24:45.512 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:24:45.512 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:24:45.517 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:24:45.517 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:24:45.517 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:24:45.517 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:24:45.517792+08:00[Asia/Shanghai]
06-24 10:24:45.640 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:24:45.683 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:24:45.712 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:24:45.741 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:24:46
06-24 10:24:45.741 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:24:46
06-24 10:24:45.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750731885741
06-24 10:24:45.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750731886000
06-24 10:24:45.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 259 毫秒
06-24 10:24:45.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:24:45.786 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:24:46.499 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:24:46.505 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:24:46.505 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:24:46.505 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:24:46.505 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:24:46.505 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:24:46.505 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:24:46.505 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@12c30824
06-24 10:24:47.704 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:24:47.709 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:24:47.750 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:24:47.796 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:24:47.812 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:24:49.006 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:24:49.006 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:24:49.006 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:24:49.016 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.969 seconds (JVM running for 8.75)
06-24 10:24:54.134 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:24:54.135 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:24:54.137 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 2 ms
06-24 10:24:55.223 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:24:55.232 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:24:55.298 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:25:00.170 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:25:00.198 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:25:00.246 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:25:00.271 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:26:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:26:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:26:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:26:00.214 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:27:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:27:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:27:00.187 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:27:00.240 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:27:20.092 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:27:20.242 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 10:27:20.242 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 10:27:20.242 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 10:27:20.242 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 10:27:20.371 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 10:27:20.392 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 10:27:24.950 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 10:27:24.952 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 25888 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 10:27:24.953 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 10:27:25.590 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 10:27:25.592 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 10:27:25.614 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 12 ms. Found 0 Redis repository interfaces.
06-24 10:27:25.773 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=e581c5bc-e588-3c7b-a240-b47499985897
06-24 10:27:26.030 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 10:27:26.038 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 10:27:26.038 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 10:27:26.038 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 10:27:26.145 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 10:27:26.145 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1166 ms
06-24 10:27:26.172 [main] INFO  com.zy.config.SessionConfig - Session Cookie配置已应用 - Profile: zl, Secure: false
06-24 10:27:26.219 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 10:27:26.793 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 10:27:27.642 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 10:27:27.642 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 10:27:27.648 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 10:27:27.648 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 10:27:27.648 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 10:27:27.648 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T10:27:27.648206500+08:00[Asia/Shanghai]
06-24 10:27:27.725 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 10:27:27.773 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 10:27:27.796 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 10:27:27.819 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T02:27:28
06-24 10:27:27.820 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T02:27:28
06-24 10:27:27.875 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750732047820
06-24 10:27:27.875 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750732048000
06-24 10:27:27.875 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 180 毫秒
06-24 10:27:27.875 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 10:27:27.875 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 10:27:28.546 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 10:27:28.551 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 10:27:28.551 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 10:27:28.551 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 10:27:28.552 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 10:27:28.552 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 10:27:28.552 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 10:27:28.552 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@15ca7322
06-24 10:27:29.742 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:27:29.747 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 10:27:29.791 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 10:27:29.853 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 10:27:29.874 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 10:27:31.049 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 10:27:31.049 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 10:27:31.049 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 10:27:31.059 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.586 seconds (JVM running for 8.3)
06-24 10:27:33.050 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 10:27:33.050 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 10:27:33.051 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
06-24 10:27:34.021 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:27:34.026 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:27:34.059 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:28:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:28:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:28:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:28:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:29:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:29:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:29:00.254 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:29:00.277 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:30:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:30:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:30:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:30:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:31:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:31:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:31:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:31:00.232 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:32:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:32:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:32:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:32:00.246 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:33:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:33:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:33:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:33:00.219 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:34:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:34:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:34:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:34:00.209 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:35:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:35:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:35:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:35:00.208 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:36:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:36:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:36:00.190 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:36:00.212 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:37:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:37:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:37:00.183 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:37:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:38:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:38:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:38:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:38:00.176 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:39:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:39:00.172 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:39:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:39:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:40:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:40:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:40:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:40:00.204 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:41:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:41:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:41:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:41:00.195 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:42:00.090 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:42:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:42:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:42:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:43:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:43:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:43:00.147 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:43:00.205 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:44:00.108 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:44:00.169 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:44:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:44:00.216 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:45:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:45:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:45:00.195 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:45:00.238 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:45:21.911 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:45:21.912 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:45:21.943 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:46:00.111 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:46:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:46:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:46:00.214 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:47:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:47:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:47:00.238 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:47:00.282 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:48:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:48:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:48:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:48:00.235 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:49:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:49:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:49:00.240 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:49:00.266 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:50:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:50:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:50:00.178 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:50:00.224 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:51:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:51:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:51:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:51:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:52:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:52:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:52:00.223 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:52:00.250 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:53:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:53:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:53:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:53:00.215 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:54:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:54:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:54:00.208 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:54:00.236 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:55:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:55:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:55:00.187 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:55:00.214 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:55:15.369 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:55:15.369 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:55:15.412 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:55:25.292 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:55:25.292 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:55:25.324 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:55:33.412 [http-nio-20000-exec-10] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:55:33.412 [http-nio-20000-exec-10] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:55:33.440 [http-nio-20000-exec-10] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:55:58.462 [http-nio-20000-exec-9] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/nonexistent
06-24 10:56:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:56:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:56:00.200 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:56:00.226 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:56:03.021 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:56:03.021 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:56:03.063 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:56:13.719 [http-nio-20000-exec-3] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/actuator/health
06-24 10:56:24.920 [http-nio-20000-exec-4] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/actuator/info
06-24 10:57:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:57:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:57:00.190 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:57:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:57:18.590 [http-nio-20000-exec-5] WARN  org.springframework.web.servlet.PageNotFound - No mapping for GET /api/actuator/health
06-24 10:57:23.446 [http-nio-20000-exec-7] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 10:57:23.446 [http-nio-20000-exec-7] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 10:57:23.476 [http-nio-20000-exec-7] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 10:58:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:58:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:58:00.193 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:58:00.257 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:59:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:59:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:59:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 10:59:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:00:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:00:00.142 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:00:00.193 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:00:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:01:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:01:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:01:00.170 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:01:00.192 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:02:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:02:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:02:00.182 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:02:00.205 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:03:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:03:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:03:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:03:00.222 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:04:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:04:00.172 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:04:00.201 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:04:00.225 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:05:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:05:00.248 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:05:00.279 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:05:00.316 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:06:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:06:00.248 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:06:00.272 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:06:00.295 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:07:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:07:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:07:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:07:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:08:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:08:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:08:00.157 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:08:00.207 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:09:00.092 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:09:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:09:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:09:00.193 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:10:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:10:00.128 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:10:00.149 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:10:00.205 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:11:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:11:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:11:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:11:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:12:00.075 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:12:00.115 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:12:00.181 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:12:00.237 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:13:00.084 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:13:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:13:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:13:00.181 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:14:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:14:00.119 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:14:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:14:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:15:00.099 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:15:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:15:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:15:00.182 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:16:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:16:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:16:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:16:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:17:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:17:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:17:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:17:00.240 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:18:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:18:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:18:00.195 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:18:00.250 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:19:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:19:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:19:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:19:00.243 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:20:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:20:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:20:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:20:00.247 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:21:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:21:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:21:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:21:00.241 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:22:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:22:00.181 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:22:00.228 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:22:00.282 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:22:17.662 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 11:22:17.847 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 11:22:17.847 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 11:22:17.847 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 11:22:17.847 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 11:22:18.015 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 11:22:18.042 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 11:22:23.943 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 11:22:23.952 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 21404 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 11:22:23.954 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 11:22:24.745 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 11:22:24.746 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 11:22:24.769 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 11 ms. Found 0 Redis repository interfaces.
06-24 11:22:24.941 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=986fe892-93de-34cf-9c2c-4a02e61a4503
06-24 11:22:25.213 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 11:22:25.220 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 11:22:25.221 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 11:22:25.221 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 11:22:25.337 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 11:22:25.337 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1339 ms
06-24 11:22:25.418 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 11:22:26.128 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 11:22:27.034 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 11:22:27.034 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 11:22:27.041 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 11:22:27.041 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 11:22:27.041 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 11:22:27.041 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T11:22:27.041971700+08:00[Asia/Shanghai]
06-24 11:22:27.126 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 11:22:27.150 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 11:22:27.176 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 11:22:27.222 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T03:22:27
06-24 11:22:27.222 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T03:22:27
06-24 11:22:27.248 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750735347223
06-24 11:22:27.249 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750735347000
06-24 11:22:27.249 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 223 毫秒
06-24 11:22:27.249 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 11:22:27.249 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 11:22:28.441 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 11:22:28.456 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 11:22:28.456 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 11:22:28.456 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 11:22:28.457 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 11:22:28.457 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 11:22:28.457 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 11:22:28.457 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@222a7429
06-24 11:22:29.678 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 11:22:29.683 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 11:22:29.739 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 11:22:29.788 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 11:22:29.812 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 11:22:30.993 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 11:22:30.993 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 11:22:30.993 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 11:22:31.003 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 8.761 seconds (JVM running for 9.56)
06-24 11:23:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:23:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:23:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:23:00.208 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:23:46.802 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 11:23:46.802 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 11:23:46.805 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 3 ms
06-24 11:23:57.514 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:23:57.520 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 11:23:57.590 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 11:24:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:24:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:24:00.188 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:24:00.239 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:24:00.373 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:24:00.373 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 11:24:00.375 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:24:00.375 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 11:24:00.398 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 11:24:00.445 [http-nio-20000-exec-6] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 11:25:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:25:00.130 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:25:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:25:00.209 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:26:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:26:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:26:00.183 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:26:00.239 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:27:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:27:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:27:00.199 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:27:00.222 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:28:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:28:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:28:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:28:00.203 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:29:00.133 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:29:00.170 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:29:00.194 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:29:00.237 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:29:01.258 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 11:29:01.424 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 11:29:01.425 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 11:29:01.425 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 11:29:01.425 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 11:29:01.550 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 11:29:01.569 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 11:29:06.340 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 11:29:06.342 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 37140 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 11:29:06.343 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 11:29:07.020 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 11:29:07.021 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 11:29:07.042 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 10 ms. Found 0 Redis repository interfaces.
06-24 11:29:07.202 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=986fe892-93de-34cf-9c2c-4a02e61a4503
06-24 11:29:07.461 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 11:29:07.468 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 11:29:07.468 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 11:29:07.468 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 11:29:07.572 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 11:29:07.572 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1200 ms
06-24 11:29:07.639 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 11:29:08.255 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 11:29:09.152 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 11:29:09.152 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 11:29:09.158 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 11:29:09.158 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 11:29:09.158 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 11:29:09.158 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T11:29:09.158763200+08:00[Asia/Shanghai]
06-24 11:29:09.216 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 11:29:09.268 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 11:29:09.289 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 11:29:09.312 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T03:29:09
06-24 11:29:09.312 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T03:29:09
06-24 11:29:09.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750735749312
06-24 11:29:09.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750735749000
06-24 11:29:09.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 312 毫秒
06-24 11:29:09.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 11:29:09.350 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 11:29:10.025 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 11:29:10.031 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 11:29:10.032 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 11:29:10.032 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 11:29:10.032 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 11:29:10.032 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 11:29:10.032 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 11:29:10.032 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1a34f8e2
06-24 11:29:11.220 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 11:29:11.225 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 11:29:11.268 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 11:29:11.320 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 11:29:11.336 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 11:29:12.516 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 11:29:12.516 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 11:29:12.516 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 11:29:12.525 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 7.666 seconds (JVM running for 8.405)
06-24 11:29:16.933 [http-nio-20000-exec-2] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 11:29:16.934 [http-nio-20000-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 11:29:16.935 [http-nio-20000-exec-2] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
06-24 11:30:00.123 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:30:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:30:00.169 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:30:00.206 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:31:00.122 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:31:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:31:00.204 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:31:00.228 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:32:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:32:00.150 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:32:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:32:00.222 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:33:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:33:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:33:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:33:00.215 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:34:00.079 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:34:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:34:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:34:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:35:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:35:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:35:00.182 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:35:00.209 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:36:00.097 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:36:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:36:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:36:00.198 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:37:00.106 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:37:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:37:00.181 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:37:00.217 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:38:00.109 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:38:00.137 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:38:00.164 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:38:00.211 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:39:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:39:00.195 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:39:00.221 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:39:00.267 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:40:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:40:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:40:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:40:00.214 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:41:00.125 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:41:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:41:00.196 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:41:00.222 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:42:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:42:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:42:00.192 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:42:00.242 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:43:00.162 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:43:00.222 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:43:00.363 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:43:00.428 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:44:00.101 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:44:00.127 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:44:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:44:00.202 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:45:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:45:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:45:00.192 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:45:00.242 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:46:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:46:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:46:00.192 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:46:00.219 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:47:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:47:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:47:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:47:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:48:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:48:00.171 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:48:00.230 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:48:00.258 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:49:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:49:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:49:00.185 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:49:00.213 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:49:19.620 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==>  Preparing: select a.CODE_,a.PCODE_,a.NAME_,a.ORD_,a.NO_TYPE_ ,(select m.NAME_ from AM_ASSET_TYPE m where m.CODE_=a.PCODE_) pname from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:49:19.630 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - ==> Parameters: 
06-24 11:49:19.670 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.list - <==      Total: 89
06-24 11:49:25.479 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:49:25.479 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 11:49:25.480 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:49:25.480 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 11:49:25.506 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 11:49:25.509 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 11:50:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:50:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:50:00.190 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:50:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:51:00.098 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:51:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:51:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:51:00.202 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:52:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:52:00.180 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:52:00.208 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:52:00.254 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:52:33.923 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:52:33.923 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:52:33.923 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 11:52:33.923 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 11:52:33.954 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 11:52:33.954 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 11:53:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:53:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:53:00.192 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:53:00.218 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:53:29.621 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:53:29.621 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 11:53:29.631 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 11:53:29.632 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 11:53:29.667 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 11:53:29.704 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 11:54:00.100 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:54:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:54:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:54:00.197 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:55:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:55:00.146 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:55:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:55:00.199 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:56:00.095 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:56:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:56:00.139 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:56:00.160 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:57:00.117 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:57:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:57:00.198 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:57:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:58:00.089 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:58:00.110 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:58:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:58:00.153 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:59:00.076 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:59:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:59:00.134 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 11:59:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:00:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:00:00.129 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:00:00.152 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:00:00.174 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:01:00.176 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:01:00.203 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:01:00.262 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:01:00.287 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:02:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:02:00.141 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:02:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:02:00.220 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:03:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:03:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:03:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:03:00.215 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:04:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:04:00.138 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:04:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:04:00.219 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:05:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:05:00.143 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:05:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:05:00.217 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:06:00.104 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:06:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:06:00.182 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:06:00.206 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:07:00.145 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:07:00.177 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:07:00.201 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:07:00.245 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:08:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:08:00.120 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:08:00.144 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:08:00.196 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:09:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:09:00.165 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:09:00.189 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:09:00.213 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:10:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:10:00.254 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:10:00.278 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:10:00.303 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:11:00.103 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:11:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:11:00.163 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:11:00.205 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:12:00.116 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:12:00.166 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:12:00.191 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:12:00.216 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 12:12:49.506 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 12:12:49.761 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 12:12:49.761 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 12:12:49.761 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 12:12:49.762 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 12:12:49.963 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 12:12:49.986 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
06-24 18:02:24.918 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
06-24 18:02:24.918 [main] INFO  com.zy.dam.DamApplication - Starting DamApplication using Java 11.0.22 on bugui with PID 19344 (F:\work\ticai\ticai-dam\target\classes started by 22315 in F:\work\ticai\ticai-dam)
06-24 18:02:24.921 [main] INFO  com.zy.dam.DamApplication - The following 1 profile is active: "zl"
06-24 18:02:25.951 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
06-24 18:02:25.953 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
06-24 18:02:25.986 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
06-24 18:02:26.266 [main] INFO  org.springframework.cloud.context.scope.GenericScope - BeanFactory id=986fe892-93de-34cf-9c2c-4a02e61a4503
06-24 18:02:26.672 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 20000 (http)
06-24 18:02:26.681 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-20000"]
06-24 18:02:26.681 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
06-24 18:02:26.681 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
06-24 18:02:26.913 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring embedded WebApplicationContext
06-24 18:02:26.913 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 1953 ms
06-24 18:02:27.032 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Starting...
06-24 18:02:27.692 [main] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Start completed.
06-24 18:02:28.974 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查开始 ===
06-24 18:02:28.975 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM默认时区: Asia/Shanghai
06-24 18:02:28.985 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区显示名称: 中国标准时间
06-24 18:02:28.986 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时区偏移量: 8 小时
06-24 18:02:28.986 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 系统默认时区ID: Asia/Shanghai
06-24 18:02:28.986 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 当前JVM时间: 2025-06-24T18:02:28.986803600+08:00[Asia/Shanghai]
06-24 18:02:29.063 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库系统时区: UTC
06-24 18:02:29.097 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库会话时区: SYSTEM
06-24 18:02:29.124 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库全局时区: SYSTEM
06-24 18:02:29.150 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库当前时间: 2025-06-24T10:02:30
06-24 18:02:29.150 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库UTC时间: 2025-06-24T10:02:30
06-24 18:02:29.190 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - JVM时间戳: 1750759349151
06-24 18:02:29.190 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 数据库时间戳: 1750759350000
06-24 18:02:29.191 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - 时间差异: 849 毫秒
06-24 18:02:29.191 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - ✅ JVM和数据库时间基本一致
06-24 18:02:29.191 [main] INFO  com.zy.dam.util.TimezoneCheckUtil - === 时区配置检查结束 ===
06-24 18:02:30.173 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
06-24 18:02:30.181 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
06-24 18:02:30.182 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
06-24 18:02:30.182 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
06-24 18:02:30.182 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

06-24 18:02:30.182 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
06-24 18:02:30.182 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
06-24 18:02:30.183 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@4bc39640
06-24 18:02:31.389 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 18:02:31.393 [main] INFO  o.s.cloud.netflix.eureka.config.DiscoveryClientOptionalArgsConfiguration - Eureka HTTP Client uses RestTemplate.
06-24 18:02:31.443 [main] WARN  o.s.c.l.config.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger - Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
06-24 18:02:31.489 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-20000"]
06-24 18:02:31.507 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 20000 (http) with context path '/api'
06-24 18:02:32.706 [main] INFO  org.springframework.cloud.commons.util.InetUtils - Cannot determine local hostname
06-24 18:02:32.706 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
06-24 18:02:32.706 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
06-24 18:02:32.716 [main] INFO  com.zy.dam.DamApplication - Started DamApplication in 9.367 seconds (JVM running for 11.055)
06-24 18:02:44.250 [http-nio-20000-exec-1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/api] - Initializing Spring DispatcherServlet 'dispatcherServlet'
06-24 18:02:44.251 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
06-24 18:02:44.252 [http-nio-20000-exec-1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 1 ms
06-24 18:02:58.594 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:02:58.601 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 18:02:58.605 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:02:58.606 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 18:02:58.644 [http-nio-20000-exec-2] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 18:02:58.654 [http-nio-20000-exec-4] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 18:03:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:03:00.121 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:03:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:03:00.175 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:04:00.083 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:04:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:04:00.135 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:04:00.159 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:05:00.078 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:05:00.102 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:05:00.126 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:05:00.151 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:05:47.958 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:05:47.958 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 18:05:47.991 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:05:47.992 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 18:05:47.992 [http-nio-20000-exec-9] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 18:05:48.039 [http-nio-20000-exec-8] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 18:06:00.087 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:06:00.112 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:06:00.154 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:06:00.179 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:06:18.284 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==>  Preparing: select a.ID_ ,a.CODE_ ,a.NAME_ ,a.DEPT_ ,a.REGION_ ,a.SCOPE_ ,a.USER_ ,a.ORD_ ,a.FLAG_ ,(select m.NAME_ from SYS_DEPT m where m.ID_=a.DEPT_) dept_name ,(select m.FULL_NAME_ from SYS_REGION m where m.CODE_=a.REGION_) region_name ,b.NAME_ user_name,b.PHONE_ user_phone from AM_REGION a left join SYS_USER b on a.USER_=b.ID_ and b.STATUS_!='9' where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:06:18.284 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - ==> Parameters: 
06-24 18:06:18.308 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==>  Preparing: select a.CODE_ id,a.PCODE_ pid,a.NAME_ label from AM_ASSET_TYPE a where a.FLAG_='1' order by a.ORD_,a.CODE_
06-24 18:06:18.309 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - ==> Parameters: 
06-24 18:06:18.310 [http-nio-20000-exec-5] DEBUG com.zy.dam.base.dao.AmRegionDAO.list - <==      Total: 22
06-24 18:06:18.334 [http-nio-20000-exec-3] DEBUG com.zy.dam.base.dao.AmAssetTypeDAO.listNode - <==      Total: 89
06-24 18:07:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:07:00.107 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:07:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:07:00.161 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:08:00.088 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:08:00.113 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:08:00.140 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:08:00.167 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:09:00.094 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:09:00.118 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:09:00.148 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:09:00.173 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:10:00.082 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:10:00.105 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:10:00.131 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:10:00.156 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:11:00.071 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:11:00.093 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:11:00.114 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:11:00.136 [scheduling-1] ERROR com.zy.dam.report.svc.RpZdjZbtSVC - 202505月份的报表已经存在
06-24 18:11:20.376 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 18:11:20.509 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
06-24 18:11:20.509 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
06-24 18:11:20.509 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
06-24 18:11:20.510 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
06-24 18:11:20.639 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown initiated...
06-24 18:11:20.662 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - zy-ds-pool - Shutdown completed.
