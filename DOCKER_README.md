# 体彩设备管理平台 Docker 部署指南

## 项目概述

本项目是基于物联网技术的体彩设备管理平台，包含以下服务：

- **ticai-dam**: 主后端服务 (Spring Boot + Java 11)
- **ticai-dam-port**: 外部接口服务 (Spring Boot + Java 11)  
- **ticai-web**: 前端 Web 应用 (Vue.js + Nginx)
- **MySQL**: 数据库服务
- **Redis**: 缓存服务

## 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

## 快速启动

### 方式一：使用启动脚本（推荐）

**Linux/macOS:**
```bash
chmod +x docker-start.sh
./docker-start.sh
```

**Windows:**
```cmd
docker-start.bat
```

### 方式二：手动启动

1. **创建必要目录**
```bash
mkdir -p data/attach logs
```

2. **启动所有服务**
```bash
docker-compose up -d
```

3. **查看服务状态**
```bash
docker-compose ps
```

## 服务访问地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 前端应用 | http://localhost | Web 管理界面 |
| 主后端服务 | http://localhost:8080/api | 主要业务接口 |
| 外部接口服务 | http://localhost:8081/port-api | 外部系统接口 |
| MySQL 数据库 | localhost:3306 | 数据库连接 |
| Redis 缓存 | localhost:6379 | 缓存服务 |

## 常用命令

### 服务管理
```bash
# 启动所有服务
docker-compose up -d

# 停止所有服务
docker-compose down

# 重启特定服务
docker-compose restart ticai-dam

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f ticai-dam
```

### 数据管理
```bash
# 备份数据库
docker exec ticai-mysql mysqldump -u root -proot123 ticai_dam > backup.sql

# 恢复数据库
docker exec -i ticai-mysql mysql -u root -proot123 ticai_dam < backup.sql

# 清理未使用的镜像和容器
docker system prune -f
```

## 配置说明

### 环境变量

可以通过环境变量覆盖默认配置：

```bash
# 数据库配置
SPRING_DATASOURCE_URL=*********************************
SPRING_DATASOURCE_USERNAME=ticai
SPRING_DATASOURCE_PASSWORD=ticai123

# Redis 配置
SPRING_REDIS_HOST=redis
SPRING_REDIS_PORT=6379
SPRING_REDIS_PASSWORD=
```

### 数据持久化

以下目录会被持久化存储：

- `mysql_data`: MySQL 数据文件
- `redis_data`: Redis 数据文件
- `./logs`: 应用日志文件
- `./data/attach`: 文件上传目录

## 开发调试

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f ticai-dam
docker-compose logs -f ticai-dam-port
docker-compose logs -f ticai-web
```

### 进入容器
```bash
# 进入后端服务容器
docker exec -it ticai-dam bash

# 进入数据库容器
docker exec -it ticai-mysql mysql -u root -proot123
```

### 重新构建镜像
```bash
# 重新构建所有镜像
docker-compose build

# 重新构建特定服务镜像
docker-compose build ticai-dam
```

## 生产环境部署

### 1. 修改配置

生产环境建议修改以下配置：

- 数据库密码
- Redis 密码
- 文件存储路径
- 日志级别

### 2. 使用外部数据库

如果使用外部数据库，可以注释掉 docker-compose.yml 中的 mysql 服务，并修改环境变量。

### 3. 负载均衡

生产环境建议在前端添加负载均衡器（如 Nginx）。

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口是否被占用：`netstat -tlnp | grep :8080`
   - 修改 docker-compose.yml 中的端口映射

2. **内存不足**
   - 增加 Docker 内存限制
   - 优化 JVM 参数

3. **数据库连接失败**
   - 检查数据库服务是否启动：`docker-compose ps`
   - 查看数据库日志：`docker-compose logs mysql`

4. **文件权限问题**
   - 确保 logs 和 data 目录有写权限
   - Linux 下执行：`chmod -R 755 logs data`

### 日志分析

主要日志文件位置：
- 应用日志：`./logs/dam.log`, `./logs/dam-port.log`
- Docker 日志：`docker-compose logs`

## 更新升级

```bash
# 停止服务
docker-compose down

# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build

# 启动服务
docker-compose up -d
```

## 联系支持

如有问题，请联系开发团队或查看项目文档。
