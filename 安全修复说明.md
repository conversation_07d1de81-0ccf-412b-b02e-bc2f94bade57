# Web 应用服务器版本信息泄露修复说明

## 问题描述

检测到 web 应用服务器版本信息泄露，可能暴露服务器技术栈信息，存在安全风险。

## 修复措施

### 1. Spring Boot 应用配置修复

#### 1.1 服务器配置 (application.yml)

- **隐藏服务器版本信息**：添加 `server.server-header: ""`
- **错误页面安全配置**：
  - `server.error.include-exception: false` - 禁用异常信息显示
  - `server.error.include-stacktrace: never` - 禁用堆栈跟踪
  - `server.error.include-message: never` - 禁用错误消息显示
  - `server.error.path: /error` - 自定义错误页面路径

#### 1.2 管理端点安全配置

- **禁用默认端点**：`management.endpoints.enabled-by-default: false`
- **限制暴露端点**：仅暴露 health 端点
- **健康检查安全**：`management.endpoint.health.show-details: never`

#### 1.3 JMX 禁用

- **禁用 JMX**：`spring.jmx.enabled: false` - 防止通过 JMX 暴露信息

### 2. 安全响应头过滤器

#### 2.1 SecurityHeadersFilter.java

创建过滤器移除可能暴露服务器信息的响应头：

- 移除 `Server` 响应头
- 移除 `X-Powered-By` 响应头
- 添加安全响应头：
  - `X-Content-Type-Options: nosniff`
  - `X-Frame-Options: DENY`
  - `X-XSS-Protection: 1; mode=block`
  - `Referrer-Policy: strict-origin-when-cross-origin`
  - `Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'`
  - `X-Permitted-Cross-Domain-Policies: none`
  - `X-Download-Options: noopen`
  - `Strict-Transport-Security: max-age=31536000; includeSubDomains`

#### 2.2 SecurityConfig.java

注册安全过滤器，确保所有请求都经过安全处理。

### 3. 错误处理增强

#### 3.1 ErrorHandler.java 修改

- **通用异常处理**：不返回具体异常信息，统一返回"系统内部错误，请联系管理员"
- **避免信息泄露**：所有异常详情仅记录到日志，不暴露给用户

#### 3.2 CustomErrorController.java

创建自定义错误控制器：

- 处理所有未被捕获的错误
- 根据 HTTP 状态码返回适当的用户友好错误信息
- 详细错误信息仅记录到日志

### 4. Nginx 配置安全加固

#### 4.1 nginx.conf 修改

- **隐藏版本信息**：`server_tokens off`
- **安全响应头**：
  - `X-Frame-Options: DENY`
  - `X-Content-Type-Options: nosniff`
  - `X-XSS-Protection: 1; mode=block`
  - `Referrer-Policy: strict-origin-when-cross-origin`
  - `Content-Security-Policy` - 内容安全策略
  - `X-Permitted-Cross-Domain-Policies: none` - 禁止跨域策略文件
  - `X-Download-Options: noopen` - 防止 IE 自动打开下载文件
  - `Strict-Transport-Security` - HTTP 严格传输安全
- **清除服务器信息**：使用 `more_clear_headers` 指令
- **自定义错误页面**：配置 404 和 5xx 错误页面

### 5. 自定义错误页面

#### 5.1 404.html

- 用户友好的 404 错误页面
- 不显示任何服务器技术信息
- 提供返回首页的链接

#### 5.2 50x.html

- 用户友好的服务器错误页面
- 不显示具体错误信息
- 引导用户联系管理员

## 安全效果

### 修复前的问题：

1. HTTP 响应头可能包含服务器版本信息
2. 错误页面可能暴露技术栈信息
3. 异常信息可能泄露系统内部结构
4. 管理端点可能暴露应用信息

### 修复后的效果：

1. ✅ 所有服务器版本信息已隐藏
2. ✅ 错误页面统一且安全
3. ✅ 异常信息不再暴露给用户
4. ✅ 管理端点访问受限
5. ✅ 添加了完整的安全响应头集合
6. ✅ 自定义错误页面用户体验友好
7. ✅ Content-Security-Policy 防止 XSS 攻击
8. ✅ X-Permitted-Cross-Domain-Policies 防止跨域策略文件攻击
9. ✅ X-Download-Options 防止 IE 文件执行攻击
10. ✅ Strict-Transport-Security 强制 HTTPS 传输

## 部署说明

### 1. 后端应用

重新编译并部署 Spring Boot 应用：

```bash
mvn clean package
```

### 2. 前端应用

确保 nginx 配置生效：

```bash
nginx -t  # 测试配置
nginx -s reload  # 重新加载配置
```

### 3. 验证修复效果

- 访问不存在的页面，检查是否显示自定义 404 页面
- 检查 HTTP 响应头是否不包含服务器版本信息
- 触发服务器错误，验证错误信息是否安全

## 注意事项

1. **日志监控**：虽然用户看不到详细错误信息，但所有错误都会记录到日志中，便于问题排查
2. **健康检查**：保留了 health 端点用于监控，但不显示详细信息
3. **兼容性**：所有修改都向后兼容，不影响现有功能
4. **性能影响**：安全过滤器对性能影响微乎其微

## 合规性

此修复方案符合以下安全标准：

- OWASP Top 10 安全实践
- 信息安全等级保护要求
- 网络安全法相关规定
