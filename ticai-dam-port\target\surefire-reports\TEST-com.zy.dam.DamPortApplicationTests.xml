<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.zy.dam.DamPortApplicationTests" time="6.424" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="java.specification.version" value="11"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="F:\work\ticai\ticai-dam-port\target\test-classes;F:\work\ticai\ticai-dam-port\target\classes;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-data-redis\2.6.6\spring-boot-starter-data-redis-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter\2.6.6\spring-boot-starter-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot\2.6.6\spring-boot-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-autoconfigure\2.6.6\spring-boot-autoconfigure-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-logging\2.6.6\spring-boot-starter-logging-2.6.6.jar;E:\maven\myRepository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;E:\maven\myRepository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;E:\maven\myRepository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;E:\maven\myRepository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;E:\maven\myRepository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;E:\maven\myRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;E:\maven\myRepository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;E:\maven\myRepository\org\springframework\data\spring-data-redis\2.6.3\spring-data-redis-2.6.3.jar;E:\maven\myRepository\org\springframework\data\spring-data-keyvalue\2.6.3\spring-data-keyvalue-2.6.3.jar;E:\maven\myRepository\org\springframework\data\spring-data-commons\2.6.3\spring-data-commons-2.6.3.jar;E:\maven\myRepository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;E:\maven\myRepository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\maven\myRepository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;E:\maven\myRepository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;E:\maven\myRepository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;E:\maven\myRepository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-validation\2.6.6\spring-boot-starter-validation-2.6.6.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;E:\maven\myRepository\org\hibernate\validator\hibernate-validator\6.2.3.Final\hibernate-validator-6.2.3.Final.jar;E:\maven\myRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;E:\maven\myRepository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;E:\maven\myRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-quartz\2.6.6\spring-boot-starter-quartz-2.6.6.jar;E:\maven\myRepository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;E:\maven\myRepository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;E:\maven\myRepository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-web\2.6.6\spring-boot-starter-web-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-json\2.6.6\spring-boot-starter-json-2.6.6.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-databind\2.13.2.2\jackson-databind-2.13.2.2.jar;E:\maven\myRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.2\jackson-datatype-jdk8-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.2\jackson-datatype-jsr310-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.2\jackson-module-parameter-names-2.13.2.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-tomcat\2.6.6\spring-boot-starter-tomcat-2.6.6.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.60\tomcat-embed-core-9.0.60.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.60\tomcat-embed-websocket-9.0.60.jar;E:\maven\myRepository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter\3.1.1\spring-cloud-starter-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-context\3.1.1\spring-cloud-context-3.1.1.jar;E:\maven\myRepository\org\springframework\security\spring-security-crypto\5.6.2\spring-security-crypto-5.6.2.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-commons\3.1.1\spring-cloud-commons-3.1.1.jar;E:\maven\myRepository\org\springframework\security\spring-security-rsa\1.0.10.RELEASE\spring-security-rsa-1.0.10.RELEASE.jar;E:\maven\myRepository\org\bouncycastle\bcpkix-jdk15on\1.68\bcpkix-jdk15on-1.68.jar;E:\maven\myRepository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\3.1.1\spring-cloud-starter-netflix-eureka-client-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-netflix-eureka-client\3.1.1\spring-cloud-netflix-eureka-client-3.1.1.jar;E:\maven\myRepository\com\netflix\eureka\eureka-client\1.10.17\eureka-client-1.10.17.jar;E:\maven\myRepository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;E:\maven\myRepository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;E:\maven\myRepository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;E:\maven\myRepository\joda-time\joda-time\2.3\joda-time-2.3.jar;E:\maven\myRepository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;E:\maven\myRepository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;E:\maven\myRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;E:\maven\myRepository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;E:\maven\myRepository\com\thoughtworks\xstream\xstream\1.4.18\xstream-1.4.18.jar;E:\maven\myRepository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;E:\maven\myRepository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;E:\maven\myRepository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;E:\maven\myRepository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;E:\maven\myRepository\com\google\guava\guava\19.0\guava-19.0.jar;E:\maven\myRepository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;E:\maven\myRepository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;E:\maven\myRepository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;E:\maven\myRepository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;E:\maven\myRepository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;E:\maven\myRepository\javax\inject\javax.inject\1\javax.inject-1.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-annotations\2.13.2\jackson-annotations-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-core\2.13.2\jackson-core-2.13.2.jar;E:\maven\myRepository\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;E:\maven\myRepository\com\netflix\eureka\eureka-core\1.10.17\eureka-core-1.10.17.jar;E:\maven\myRepository\com\fasterxml\woodstox\woodstox-core\6.2.1\woodstox-core-6.2.1.jar;E:\maven\myRepository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.1\spring-cloud-starter-loadbalancer-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.1\spring-cloud-loadbalancer-3.1.1.jar;E:\maven\myRepository\io\projectreactor\addons\reactor-extra\3.4.7\reactor-extra-3.4.7.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-cache\2.6.6\spring-boot-starter-cache-2.6.6.jar;E:\maven\myRepository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.1\spring-cloud-starter-openfeign-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.1\spring-cloud-openfeign-core-3.1.1.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-aop\2.6.6\spring-boot-starter-aop-2.6.6.jar;E:\maven\myRepository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;E:\maven\myRepository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;E:\maven\myRepository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;E:\maven\myRepository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;E:\maven\myRepository\io\github\openfeign\feign-core\11.8\feign-core-11.8.jar;E:\maven\myRepository\io\github\openfeign\feign-slf4j\11.8\feign-slf4j-11.8.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-ui\1.6.3\springdoc-openapi-ui-1.6.3.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-webmvc-core\1.6.3\springdoc-openapi-webmvc-core-1.6.3.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-common\1.6.3\springdoc-openapi-common-1.6.3.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-models\2.1.12\swagger-models-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-annotations\2.1.12\swagger-annotations-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-integration\2.1.12\swagger-integration-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-core\2.1.12\swagger-core-2.1.12.jar;E:\maven\myRepository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.2\jackson-dataformat-yaml-2.13.2.jar;E:\maven\myRepository\io\github\classgraph\classgraph\4.8.117\classgraph-4.8.117.jar;E:\maven\myRepository\io\github\toolfactory\jvm-driver\4.0.0\jvm-driver-4.0.0.jar;E:\maven\myRepository\io\github\toolfactory\narcissus\1.0.1\narcissus-1.0.1.jar;E:\maven\myRepository\org\webjars\swagger-ui\4.1.3\swagger-ui-4.1.3.jar;E:\maven\myRepository\org\webjars\webjars-locator-core\0.48\webjars-locator-core-0.48.jar;E:\maven\myRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-jdbc\2.6.6\spring-boot-starter-jdbc-2.6.6.jar;E:\maven\myRepository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;E:\maven\myRepository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;E:\maven\myRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;E:\maven\myRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;E:\maven\myRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;E:\maven\myRepository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;E:\maven\myRepository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;F:\work\ticai\ticai-dam-port\libs\zy-core-1.0.5.jar;E:\maven\myRepository\com\aliyun\dysmsapi20170525\2.0.9\dysmsapi20170525-2.0.9.jar;E:\maven\myRepository\com\aliyun\tea-util\0.2.13\tea-util-0.2.13.jar;E:\maven\myRepository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;E:\maven\myRepository\com\aliyun\endpoint-util\0.0.6\endpoint-util-0.0.6.jar;E:\maven\myRepository\com\aliyun\tea\1.1.14\tea-1.1.14.jar;E:\maven\myRepository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;E:\maven\myRepository\com\squareup\okio\okio\1.17.2\okio-1.17.2.jar;E:\maven\myRepository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;E:\maven\myRepository\com\aliyun\tea-openapi\0.2.2\tea-openapi-0.2.2.jar;E:\maven\myRepository\com\aliyun\credentials-java\0.2.4\credentials-java-0.2.4.jar;E:\maven\myRepository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;E:\maven\myRepository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;E:\maven\myRepository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;E:\maven\myRepository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;E:\maven\myRepository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;E:\maven\myRepository\com\aliyun\alibabacloud-gateway-spi\0.0.1\alibabacloud-gateway-spi-0.0.1.jar;E:\maven\myRepository\com\aliyun\openapiutil\0.1.14\openapiutil-0.1.14.jar;E:\maven\myRepository\org\jsoup\jsoup\1.14.3\jsoup-1.14.3.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit\2.60.0\htmlunit-2.60.0.jar;E:\maven\myRepository\xalan\xalan\2.7.2\xalan-2.7.2.jar;E:\maven\myRepository\xalan\serializer\2.7.2\serializer-2.7.2.jar;E:\maven\myRepository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit-core-js\2.60.0\htmlunit-core-js-2.60.0.jar;E:\maven\myRepository\net\sourceforge\htmlunit\neko-htmlunit\2.60.0\neko-htmlunit-2.60.0.jar;E:\maven\myRepository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;E:\maven\myRepository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit-cssparser\1.11.0\htmlunit-cssparser-1.11.0.jar;E:\maven\myRepository\org\apache\commons\commons-text\1.9\commons-text-1.9.jar;E:\maven\myRepository\commons-io\commons-io\2.10.0\commons-io-2.10.0.jar;E:\maven\myRepository\commons-net\commons-net\3.8.0\commons-net-3.8.0.jar;E:\maven\myRepository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;E:\maven\myRepository\org\brotli\dec\0.1.2\dec-0.1.2.jar;E:\maven\myRepository\com\shapesecurity\salvation2\3.0.0\salvation2-3.0.0.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;E:\maven\myRepository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-test\2.6.6\spring-boot-starter-test-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-test\2.6.6\spring-boot-test-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.6\spring-boot-test-autoconfigure-2.6.6.jar;E:\maven\myRepository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;E:\maven\myRepository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;E:\maven\myRepository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;E:\maven\myRepository\org\ow2\asm\asm\9.1\asm-9.1.jar;E:\maven\myRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;E:\maven\myRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;E:\maven\myRepository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;E:\maven\myRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;E:\maven\myRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;E:\maven\myRepository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;E:\maven\myRepository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;E:\maven\myRepository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;E:\maven\myRepository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;E:\maven\myRepository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;E:\maven\myRepository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;E:\maven\myRepository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;E:\maven\myRepository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;E:\maven\myRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;E:\maven\myRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;E:\maven\myRepository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;E:\maven\myRepository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://openjdk.java.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="11"/>
    <property name="os.name" value="Windows 11"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Java\jdk11\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire4806753946689234874\surefirebooter16733731017026423750.jar C:\Users\<USER>\AppData\Local\Temp\surefire4806753946689234874 2025-06-26T16-20-27_726-jvmRun1 surefire17942755228798827918tmp surefire_014800052186372605870tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="F:\work\ticai\ticai-dam-port\target\test-classes;F:\work\ticai\ticai-dam-port\target\classes;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-data-redis\2.6.6\spring-boot-starter-data-redis-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter\2.6.6\spring-boot-starter-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot\2.6.6\spring-boot-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-autoconfigure\2.6.6\spring-boot-autoconfigure-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-logging\2.6.6\spring-boot-starter-logging-2.6.6.jar;E:\maven\myRepository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;E:\maven\myRepository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;E:\maven\myRepository\org\apache\logging\log4j\log4j-to-slf4j\2.17.2\log4j-to-slf4j-2.17.2.jar;E:\maven\myRepository\org\apache\logging\log4j\log4j-api\2.17.2\log4j-api-2.17.2.jar;E:\maven\myRepository\org\slf4j\jul-to-slf4j\1.7.36\jul-to-slf4j-1.7.36.jar;E:\maven\myRepository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;E:\maven\myRepository\org\yaml\snakeyaml\1.29\snakeyaml-1.29.jar;E:\maven\myRepository\org\springframework\data\spring-data-redis\2.6.3\spring-data-redis-2.6.3.jar;E:\maven\myRepository\org\springframework\data\spring-data-keyvalue\2.6.3\spring-data-keyvalue-2.6.3.jar;E:\maven\myRepository\org\springframework\data\spring-data-commons\2.6.3\spring-data-commons-2.6.3.jar;E:\maven\myRepository\org\springframework\spring-oxm\5.3.18\spring-oxm-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-aop\5.3.18\spring-aop-5.3.18.jar;E:\maven\myRepository\org\slf4j\slf4j-api\1.7.36\slf4j-api-1.7.36.jar;E:\maven\myRepository\io\lettuce\lettuce-core\6.1.8.RELEASE\lettuce-core-6.1.8.RELEASE.jar;E:\maven\myRepository\io\netty\netty-common\4.1.75.Final\netty-common-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-handler\4.1.75.Final\netty-handler-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-resolver\4.1.75.Final\netty-resolver-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-buffer\4.1.75.Final\netty-buffer-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-codec\4.1.75.Final\netty-codec-4.1.75.Final.jar;E:\maven\myRepository\io\netty\netty-transport\4.1.75.Final\netty-transport-4.1.75.Final.jar;E:\maven\myRepository\io\projectreactor\reactor-core\3.4.16\reactor-core-3.4.16.jar;E:\maven\myRepository\org\reactivestreams\reactive-streams\1.0.3\reactive-streams-1.0.3.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-validation\2.6.6\spring-boot-starter-validation-2.6.6.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-el\9.0.60\tomcat-embed-el-9.0.60.jar;E:\maven\myRepository\org\hibernate\validator\hibernate-validator\6.2.3.Final\hibernate-validator-6.2.3.Final.jar;E:\maven\myRepository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;E:\maven\myRepository\org\jboss\logging\jboss-logging\3.4.3.Final\jboss-logging-3.4.3.Final.jar;E:\maven\myRepository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-quartz\2.6.6\spring-boot-starter-quartz-2.6.6.jar;E:\maven\myRepository\org\springframework\spring-context-support\5.3.18\spring-context-support-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-beans\5.3.18\spring-beans-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-context\5.3.18\spring-context-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-tx\5.3.18\spring-tx-5.3.18.jar;E:\maven\myRepository\org\quartz-scheduler\quartz\2.3.2\quartz-2.3.2.jar;E:\maven\myRepository\com\mchange\mchange-commons-java\0.2.15\mchange-commons-java-0.2.15.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-web\2.6.6\spring-boot-starter-web-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-json\2.6.6\spring-boot-starter-json-2.6.6.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-databind\2.13.2.2\jackson-databind-2.13.2.2.jar;E:\maven\myRepository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.2\jackson-datatype-jdk8-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.2\jackson-datatype-jsr310-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.2\jackson-module-parameter-names-2.13.2.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-tomcat\2.6.6\spring-boot-starter-tomcat-2.6.6.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-core\9.0.60\tomcat-embed-core-9.0.60.jar;E:\maven\myRepository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.60\tomcat-embed-websocket-9.0.60.jar;E:\maven\myRepository\org\springframework\spring-web\5.3.18\spring-web-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-webmvc\5.3.18\spring-webmvc-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-expression\5.3.18\spring-expression-5.3.18.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter\3.1.1\spring-cloud-starter-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-context\3.1.1\spring-cloud-context-3.1.1.jar;E:\maven\myRepository\org\springframework\security\spring-security-crypto\5.6.2\spring-security-crypto-5.6.2.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-commons\3.1.1\spring-cloud-commons-3.1.1.jar;E:\maven\myRepository\org\springframework\security\spring-security-rsa\1.0.10.RELEASE\spring-security-rsa-1.0.10.RELEASE.jar;E:\maven\myRepository\org\bouncycastle\bcpkix-jdk15on\1.68\bcpkix-jdk15on-1.68.jar;E:\maven\myRepository\org\bouncycastle\bcprov-jdk15on\1.68\bcprov-jdk15on-1.68.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-netflix-eureka-client\3.1.1\spring-cloud-starter-netflix-eureka-client-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-netflix-eureka-client\3.1.1\spring-cloud-netflix-eureka-client-3.1.1.jar;E:\maven\myRepository\com\netflix\eureka\eureka-client\1.10.17\eureka-client-1.10.17.jar;E:\maven\myRepository\com\netflix\netflix-commons\netflix-eventbus\0.3.0\netflix-eventbus-0.3.0.jar;E:\maven\myRepository\com\netflix\netflix-commons\netflix-infix\0.3.0\netflix-infix-0.3.0.jar;E:\maven\myRepository\commons-jxpath\commons-jxpath\1.3\commons-jxpath-1.3.jar;E:\maven\myRepository\joda-time\joda-time\2.3\joda-time-2.3.jar;E:\maven\myRepository\org\antlr\antlr-runtime\3.4\antlr-runtime-3.4.jar;E:\maven\myRepository\org\antlr\stringtemplate\3.2.1\stringtemplate-3.2.1.jar;E:\maven\myRepository\antlr\antlr\2.7.7\antlr-2.7.7.jar;E:\maven\myRepository\org\apache\commons\commons-math\2.2\commons-math-2.2.jar;E:\maven\myRepository\com\thoughtworks\xstream\xstream\1.4.18\xstream-1.4.18.jar;E:\maven\myRepository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;E:\maven\myRepository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;E:\maven\myRepository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;E:\maven\myRepository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;E:\maven\myRepository\com\google\guava\guava\19.0\guava-19.0.jar;E:\maven\myRepository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;E:\maven\myRepository\org\apache\httpcomponents\httpcore\4.4.15\httpcore-4.4.15.jar;E:\maven\myRepository\commons-configuration\commons-configuration\1.10\commons-configuration-1.10.jar;E:\maven\myRepository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;E:\maven\myRepository\com\google\inject\guice\4.1.0\guice-4.1.0.jar;E:\maven\myRepository\javax\inject\javax.inject\1\javax.inject-1.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-annotations\2.13.2\jackson-annotations-2.13.2.jar;E:\maven\myRepository\com\fasterxml\jackson\core\jackson-core\2.13.2\jackson-core-2.13.2.jar;E:\maven\myRepository\org\codehaus\jettison\jettison\1.4.0\jettison-1.4.0.jar;E:\maven\myRepository\com\netflix\eureka\eureka-core\1.10.17\eureka-core-1.10.17.jar;E:\maven\myRepository\com\fasterxml\woodstox\woodstox-core\6.2.1\woodstox-core-6.2.1.jar;E:\maven\myRepository\org\codehaus\woodstox\stax2-api\4.2.1\stax2-api-4.2.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-loadbalancer\3.1.1\spring-cloud-starter-loadbalancer-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-loadbalancer\3.1.1\spring-cloud-loadbalancer-3.1.1.jar;E:\maven\myRepository\io\projectreactor\addons\reactor-extra\3.4.7\reactor-extra-3.4.7.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-cache\2.6.6\spring-boot-starter-cache-2.6.6.jar;E:\maven\myRepository\com\stoyanr\evictor\1.0.0\evictor-1.0.0.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-starter-openfeign\3.1.1\spring-cloud-starter-openfeign-3.1.1.jar;E:\maven\myRepository\org\springframework\cloud\spring-cloud-openfeign-core\3.1.1\spring-cloud-openfeign-core-3.1.1.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-aop\2.6.6\spring-boot-starter-aop-2.6.6.jar;E:\maven\myRepository\org\aspectj\aspectjweaver\1.9.7\aspectjweaver-1.9.7.jar;E:\maven\myRepository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;E:\maven\myRepository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;E:\maven\myRepository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;E:\maven\myRepository\io\github\openfeign\feign-core\11.8\feign-core-11.8.jar;E:\maven\myRepository\io\github\openfeign\feign-slf4j\11.8\feign-slf4j-11.8.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-ui\1.6.3\springdoc-openapi-ui-1.6.3.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-webmvc-core\1.6.3\springdoc-openapi-webmvc-core-1.6.3.jar;E:\maven\myRepository\org\springdoc\springdoc-openapi-common\1.6.3\springdoc-openapi-common-1.6.3.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-models\2.1.12\swagger-models-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-annotations\2.1.12\swagger-annotations-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-integration\2.1.12\swagger-integration-2.1.12.jar;E:\maven\myRepository\io\swagger\core\v3\swagger-core\2.1.12\swagger-core-2.1.12.jar;E:\maven\myRepository\com\fasterxml\jackson\dataformat\jackson-dataformat-yaml\2.13.2\jackson-dataformat-yaml-2.13.2.jar;E:\maven\myRepository\io\github\classgraph\classgraph\4.8.117\classgraph-4.8.117.jar;E:\maven\myRepository\io\github\toolfactory\jvm-driver\4.0.0\jvm-driver-4.0.0.jar;E:\maven\myRepository\io\github\toolfactory\narcissus\1.0.1\narcissus-1.0.1.jar;E:\maven\myRepository\org\webjars\swagger-ui\4.1.3\swagger-ui-4.1.3.jar;E:\maven\myRepository\org\webjars\webjars-locator-core\0.48\webjars-locator-core-0.48.jar;E:\maven\myRepository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.2.0\mybatis-spring-boot-starter-2.2.0.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-jdbc\2.6.6\spring-boot-starter-jdbc-2.6.6.jar;E:\maven\myRepository\com\zaxxer\HikariCP\4.0.3\HikariCP-4.0.3.jar;E:\maven\myRepository\org\springframework\spring-jdbc\5.3.18\spring-jdbc-5.3.18.jar;E:\maven\myRepository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.2.0\mybatis-spring-boot-autoconfigure-2.2.0.jar;E:\maven\myRepository\org\mybatis\mybatis\3.5.7\mybatis-3.5.7.jar;E:\maven\myRepository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;E:\maven\myRepository\mysql\mysql-connector-java\8.0.28\mysql-connector-java-8.0.28.jar;E:\maven\myRepository\org\projectlombok\lombok\1.18.22\lombok-1.18.22.jar;F:\work\ticai\ticai-dam-port\libs\zy-core-1.0.5.jar;E:\maven\myRepository\com\aliyun\dysmsapi20170525\2.0.9\dysmsapi20170525-2.0.9.jar;E:\maven\myRepository\com\aliyun\tea-util\0.2.13\tea-util-0.2.13.jar;E:\maven\myRepository\com\google\code\gson\gson\2.8.9\gson-2.8.9.jar;E:\maven\myRepository\com\aliyun\endpoint-util\0.0.6\endpoint-util-0.0.6.jar;E:\maven\myRepository\com\aliyun\tea\1.1.14\tea-1.1.14.jar;E:\maven\myRepository\com\squareup\okhttp3\okhttp\3.14.9\okhttp-3.14.9.jar;E:\maven\myRepository\com\squareup\okio\okio\1.17.2\okio-1.17.2.jar;E:\maven\myRepository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;E:\maven\myRepository\com\aliyun\tea-openapi\0.2.2\tea-openapi-0.2.2.jar;E:\maven\myRepository\com\aliyun\credentials-java\0.2.4\credentials-java-0.2.4.jar;E:\maven\myRepository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;E:\maven\myRepository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;E:\maven\myRepository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;E:\maven\myRepository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;E:\maven\myRepository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;E:\maven\myRepository\com\aliyun\alibabacloud-gateway-spi\0.0.1\alibabacloud-gateway-spi-0.0.1.jar;E:\maven\myRepository\com\aliyun\openapiutil\0.1.14\openapiutil-0.1.14.jar;E:\maven\myRepository\org\jsoup\jsoup\1.14.3\jsoup-1.14.3.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit\2.60.0\htmlunit-2.60.0.jar;E:\maven\myRepository\xalan\xalan\2.7.2\xalan-2.7.2.jar;E:\maven\myRepository\xalan\serializer\2.7.2\serializer-2.7.2.jar;E:\maven\myRepository\org\apache\httpcomponents\httpmime\4.5.13\httpmime-4.5.13.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit-core-js\2.60.0\htmlunit-core-js-2.60.0.jar;E:\maven\myRepository\net\sourceforge\htmlunit\neko-htmlunit\2.60.0\neko-htmlunit-2.60.0.jar;E:\maven\myRepository\xerces\xercesImpl\2.12.2\xercesImpl-2.12.2.jar;E:\maven\myRepository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;E:\maven\myRepository\net\sourceforge\htmlunit\htmlunit-cssparser\1.11.0\htmlunit-cssparser-1.11.0.jar;E:\maven\myRepository\org\apache\commons\commons-text\1.9\commons-text-1.9.jar;E:\maven\myRepository\commons-io\commons-io\2.10.0\commons-io-2.10.0.jar;E:\maven\myRepository\commons-net\commons-net\3.8.0\commons-net-3.8.0.jar;E:\maven\myRepository\commons-codec\commons-codec\1.15\commons-codec-1.15.jar;E:\maven\myRepository\org\brotli\dec\0.1.2\dec-0.1.2.jar;E:\maven\myRepository\com\shapesecurity\salvation2\3.0.0\salvation2-3.0.0.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-client\9.4.45.v20220203\websocket-client-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-client\9.4.45.v20220203\jetty-client-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-http\9.4.45.v20220203\jetty-http-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-util\9.4.45.v20220203\jetty-util-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\jetty-io\9.4.45.v20220203\jetty-io-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-common\9.4.45.v20220203\websocket-common-9.4.45.v20220203.jar;E:\maven\myRepository\org\eclipse\jetty\websocket\websocket-api\9.4.45.v20220203\websocket-api-9.4.45.v20220203.jar;E:\maven\myRepository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-starter-test\2.6.6\spring-boot-starter-test-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-test\2.6.6\spring-boot-test-2.6.6.jar;E:\maven\myRepository\org\springframework\boot\spring-boot-test-autoconfigure\2.6.6\spring-boot-test-autoconfigure-2.6.6.jar;E:\maven\myRepository\com\jayway\jsonpath\json-path\2.6.0\json-path-2.6.0.jar;E:\maven\myRepository\net\minidev\json-smart\2.4.8\json-smart-2.4.8.jar;E:\maven\myRepository\net\minidev\accessors-smart\2.4.8\accessors-smart-2.4.8.jar;E:\maven\myRepository\org\ow2\asm\asm\9.1\asm-9.1.jar;E:\maven\myRepository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;E:\maven\myRepository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;E:\maven\myRepository\org\assertj\assertj-core\3.21.0\assertj-core-3.21.0.jar;E:\maven\myRepository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter\5.8.2\junit-jupiter-5.8.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-api\5.8.2\junit-jupiter-api-5.8.2.jar;E:\maven\myRepository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;E:\maven\myRepository\org\junit\platform\junit-platform-commons\1.8.2\junit-platform-commons-1.8.2.jar;E:\maven\myRepository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-params\5.8.2\junit-jupiter-params-5.8.2.jar;E:\maven\myRepository\org\junit\jupiter\junit-jupiter-engine\5.8.2\junit-jupiter-engine-5.8.2.jar;E:\maven\myRepository\org\junit\platform\junit-platform-engine\1.8.2\junit-platform-engine-1.8.2.jar;E:\maven\myRepository\org\mockito\mockito-core\4.0.0\mockito-core-4.0.0.jar;E:\maven\myRepository\net\bytebuddy\byte-buddy\1.11.22\byte-buddy-1.11.22.jar;E:\maven\myRepository\net\bytebuddy\byte-buddy-agent\1.11.22\byte-buddy-agent-1.11.22.jar;E:\maven\myRepository\org\objenesis\objenesis\3.2\objenesis-3.2.jar;E:\maven\myRepository\org\mockito\mockito-junit-jupiter\4.0.0\mockito-junit-jupiter-4.0.0.jar;E:\maven\myRepository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;E:\maven\myRepository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;E:\maven\myRepository\org\springframework\spring-core\5.3.18\spring-core-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-jcl\5.3.18\spring-jcl-5.3.18.jar;E:\maven\myRepository\org\springframework\spring-test\5.3.18\spring-test-5.3.18.jar;E:\maven\myRepository\org\xmlunit\xmlunit-core\2.8.4\xmlunit-core-2.8.4.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Java\jdk11"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="F:\work\ticai\ticai-dam-port"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire4806753946689234874\surefirebooter16733731017026423750.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="11.0.22+9-LTS-219"/>
    <property name="user.name" value="22315"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="java.vendor.version" value="18.9"/>
    <property name="localRepository" value="E:\maven\myRepository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2025.1"/>
    <property name="java.version" value="11.0.22"/>
    <property name="user.dir" value="F:\work\ticai\ticai-dam-port"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\Java\jdk11\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;D:\VMware\bin\;E:\python\Scripts\;E:\python\;D:\Java\jdk11\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Git\Git\cmd;E:\gcc\x86_64-8.1.0-release-posix-sjlj-rt_v6-rev0\mingw64\bin;E:\maven\apache-maven-3.8.1\bin;E:\MySQL\MySQL Server 8.0\bin;E:\AnaConda;E:\AnaConda\Scripts;E:\AnaConda\Library\bin;E:\AnaConda\Library\mingw-w64\bin;C:\Program Files\dotnet\;D:\VSCode\VSCode-win32-x64-1.86.1\bin;D:\nvm\nvm;D:\node;D:\nvm\node_global;d:\Cursor\cursor\resources\app\bin;D:\Wechat_devtools\微信web开发者工具\dll;;C:\Program Files\Docker\Docker\resources\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;D:\Idea\IntelliJ IDEA 2025.1\bin;;E:\pycharm\PyCharm Community Edition 2024.1\bin;;F:\Ollama;C:\Users\<USER>\AppData\Roaming\npm;D:\nvm\nvm;D:\node;D:\Cursor\cursor\resources\app\bin;E:\DataGrip\DataGrip 2025.1\bin;;."/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vm.version" value="11.0.22+9-LTS-219"/>
    <property name="java.specification.maintenance.version" value="2"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="55.0"/>
  </properties>
  <testcase name="contextLoads" classname="com.zy.dam.DamPortApplicationTests" time="0.644"/>
</testsuite>